package com.kevinnovation.detoxlauncher.data

import android.content.Context
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.kevinnovation.detoxlauncher.viewmodel.TEXT_ALIGN
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

val Context.dataStore by preferencesDataStore("app_preferences")

class PreferencesManager(private val context: Context) {
    companion object {
        val FAVORITE_APPS = stringSetPreferencesKey("favorite_apps")
        val SWIPE_RIGHT_APP = stringPreferencesKey("swipe_right_app")
        val SWIPE_LEFT_APP = stringPreferencesKey("swipe_left_app")
        val TEXT_SIZE = intPreferencesKey("text_size")
        val FONT_WEIGHT_CLOCK = intPreferencesKey("font_weight_clock")
        val FONT_WEIGHT_DATE = intPreferencesKey("font_weight_date")
        val FONT_WEIGHT_TEXTS = intPreferencesKey("font_weight_texts")
        val TEXT_ALIGNMENT = stringPreferencesKey("text_alignment")
        val TEXT_SPACING = intPreferencesKey("text_spacing")
        val TEXT_COLOR = intPreferencesKey("text_color")
        val PRIMARY_COLOR = intPreferencesKey("primary_color")
        val SECONDARY_COLOR = intPreferencesKey("secondary_color")
        val SHOW_STATUS_BAR = booleanPreferencesKey("show_status_bar")
        val SHOW_FULL_WEEKDAY = booleanPreferencesKey("show_full_weekday")
        val LOCK_PORTRAIT_ORIENTATION = booleanPreferencesKey("lock_portrait_orientation")
        val CUSTOM_APP_NAMES = stringPreferencesKey("custom_app_names")
        val HIDDEN_APPS = stringSetPreferencesKey("hidden_apps")
        val SHOW_HIDDEN_APPS = booleanPreferencesKey("show_hidden_apps")
        val HIGHLIGHT_MODIFIED_APPS = booleanPreferencesKey("highlight_modified_apps")
        val PREFERRED_CLOCK_APP = stringPreferencesKey("preferred_clock_app")
        val PREFERRED_CALENDAR_APP = stringPreferencesKey("preferred_calendar_app")

        // Enhanced color preferences
        val GRADIENT_DIRECTION = stringPreferencesKey("gradient_direction")
        val CUSTOM_GRADIENT_ANGLE = floatPreferencesKey("custom_gradient_angle")
        val TERTIARY_COLOR = intPreferencesKey("tertiary_color")
        val CURRENT_COLOR_THEME = stringPreferencesKey("current_color_theme")
        val FAVORITE_COLOR_COMBINATIONS = stringPreferencesKey("favorite_color_combinations")
        val ACCESSIBILITY_HIGH_CONTRAST = booleanPreferencesKey("accessibility_high_contrast")
        val ACCESSIBILITY_COLOR_BLIND_FRIENDLY = booleanPreferencesKey("accessibility_color_blind_friendly")
        val ACCESSIBILITY_AUTO_BRIGHTNESS = booleanPreferencesKey("accessibility_auto_brightness")
        val ACCESSIBILITY_MIN_CONTRAST_RATIO = floatPreferencesKey("accessibility_min_contrast_ratio")
        val TIME_BASED_COLORS_ENABLED = booleanPreferencesKey("time_based_colors_enabled")
        val ANIMATED_GRADIENTS_ENABLED = booleanPreferencesKey("animated_gradients_enabled")

        var IS_LEFT = false
    }

    // Flow der favorisierten Package-Namen
    val favoriteAppsFlow: Flow<Set<String>> = context.dataStore.data.map { prefs ->
        prefs[FAVORITE_APPS] ?: emptySet()
    }

    private val DEFAULT_TEXT_SPACING = 12

    // Flow for the font weight preference
    val textSpacingFlow: Flow<Int> = context.dataStore.data.map { prefs ->
        prefs[TEXT_SPACING] ?: DEFAULT_TEXT_SPACING
    }

    // Flow for app Name
    val swipeLeftApp: Flow<AppModel> = context.dataStore.data.map { prefs ->
        val appString = prefs[SWIPE_LEFT_APP] ?: "Select App;NULL"
        AppModel.fromString(appString)
    }

    // Flow for app Name
    val swipeRightApp: Flow<AppModel> = context.dataStore.data.map { prefs ->
        val appString = prefs[SWIPE_RIGHT_APP] ?: "Select App;NULL"
        AppModel.fromString(appString)
    }

    // Flow for the font weight preference
    val fontWeightClockFlow: Flow<FontWeight> = context.dataStore.data.map { prefs ->
        val weight = prefs[FONT_WEIGHT_CLOCK] ?: FontWeight.W700.weight
        FontWeight(weight)
    }

    val fontWeightDateFlow: Flow<FontWeight> = context.dataStore.data.map { prefs ->
        val weight = prefs[FONT_WEIGHT_DATE] ?: FontWeight.W700.weight
        FontWeight(weight)
    }

    val fontWeightTextsFlow: Flow<FontWeight> = context.dataStore.data.map { prefs ->
        val weight = prefs[FONT_WEIGHT_TEXTS] ?: FontWeight.W200.weight
        FontWeight(weight)
    }

    val textAlignment: Flow<TEXT_ALIGN> = context.dataStore.data.map { prefs ->
        val textAlign = prefs[TEXT_ALIGNMENT] ?: TEXT_ALIGN.CENTER.name
        TEXT_ALIGN.valueOf(textAlign)
    }

    val textSize: Flow<Int> = context.dataStore.data.map { prefs ->
        prefs[TEXT_SIZE] ?: 32
    }

    val textColorFlow: Flow<Color> = context.dataStore.data.map { prefs ->
        val argb = prefs[TEXT_COLOR] ?: Color.White.toArgb()
        Color(argb)
    }

    val primaryColorFlow: Flow<Color> = context.dataStore.data.map { prefs ->
        val argb = prefs[PRIMARY_COLOR] ?: Color.White.toArgb()
        Color(argb)
    }

    val secondaryColorFlow: Flow<Color> = context.dataStore.data.map { prefs ->
        val argb = prefs[SECONDARY_COLOR] ?: Color.White.toArgb()
        Color(argb)
    }

    // Enhanced color flows
    val tertiaryColorFlow: Flow<Color?> = context.dataStore.data.map { prefs ->
        val argb = prefs[TERTIARY_COLOR]
        if (argb != null) Color(argb) else null
    }

    val gradientDirectionFlow: Flow<GradientDirection> = context.dataStore.data.map { prefs ->
        val direction = prefs[GRADIENT_DIRECTION] ?: GradientDirection.HORIZONTAL.name
        try {
            GradientDirection.valueOf(direction)
        } catch (e: IllegalArgumentException) {
            GradientDirection.HORIZONTAL
        }
    }

    val customGradientAngleFlow: Flow<Float> = context.dataStore.data.map { prefs ->
        prefs[CUSTOM_GRADIENT_ANGLE] ?: 0f
    }

    val currentColorThemeFlow: Flow<String?> = context.dataStore.data.map { prefs ->
        prefs[CURRENT_COLOR_THEME]
    }

    val favoriteColorCombinationsFlow: Flow<String> = context.dataStore.data.map { prefs ->
        prefs[FAVORITE_COLOR_COMBINATIONS] ?: ""
    }

    val accessibilityHighContrastFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[ACCESSIBILITY_HIGH_CONTRAST] ?: false
    }

    val accessibilityColorBlindFriendlyFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[ACCESSIBILITY_COLOR_BLIND_FRIENDLY] ?: false
    }

    val accessibilityAutoBrightnessFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[ACCESSIBILITY_AUTO_BRIGHTNESS] ?: false
    }

    val accessibilityMinContrastRatioFlow: Flow<Float> = context.dataStore.data.map { prefs ->
        prefs[ACCESSIBILITY_MIN_CONTRAST_RATIO] ?: 4.5f
    }

    val timeBasedColorsEnabledFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[TIME_BASED_COLORS_ENABLED] ?: false
    }

    val animatedGradientsEnabledFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[ANIMATED_GRADIENTS_ENABLED] ?: false
    }

    val showFullWeekday: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[SHOW_FULL_WEEKDAY] ?: false
    }

    val showStatusBar: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[SHOW_STATUS_BAR] ?: false
    }

    val lockPortraitOrientation: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[LOCK_PORTRAIT_ORIENTATION] ?: true
    }

    // Flow für benutzerdefinierte App-Namen als Map von packageName zu customName
    val customAppNamesFlow: Flow<Map<String, String>> = context.dataStore.data.map { prefs ->
        val customNamesString = prefs[CUSTOM_APP_NAMES] ?: ""
        if (customNamesString.isEmpty()) {
            emptyMap()
        } else {
            customNamesString.split("|").associate {
                val parts = it.split(":")
                if (parts.size == 2) {
                    parts[0] to parts[1]
                } else {
                    "" to ""
                }
            }.filterKeys { it.isNotEmpty() }
        }
    }

    // Flow der ausgeblendeten Apps (als Set von Package-Namen)
    val hiddenAppsFlow: Flow<Set<String>> = context.dataStore.data.map { prefs ->
        prefs[HIDDEN_APPS] ?: emptySet()
    }

    // Flow für die Einstellung, ob ausgeblendete Apps angezeigt werden sollen
    val showHiddenAppsFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[SHOW_HIDDEN_APPS] ?: false
    }

    // Flow für die Einstellung, ob modifizierte Apps hervorgehoben werden sollen
    val highlightModifiedAppsFlow: Flow<Boolean> = context.dataStore.data.map { prefs ->
        prefs[HIGHLIGHT_MODIFIED_APPS] ?: false
    }

    // Flow für die bevorzugte Uhr-App
    val preferredClockAppFlow: Flow<AppModel?> = context.dataStore.data.map { prefs ->
        val appString = prefs[PREFERRED_CLOCK_APP]
        if (appString != null) AppModel.fromString(appString) else null
    }

    // Flow für die bevorzugte Kalender-App
    val preferredCalendarAppFlow: Flow<AppModel?> = context.dataStore.data.map { prefs ->
        val appString = prefs[PREFERRED_CALENDAR_APP]
        if (appString != null) AppModel.fromString(appString) else null
    }

    // Funktion zum Hinzufügen einer App zu den Favoriten
    suspend fun addFavoriteApp(packageName: String) {
        context.dataStore.edit { prefs ->
            val current = prefs[FAVORITE_APPS] ?: emptySet()
            prefs[FAVORITE_APPS] = current + packageName
        }
    }

    // Funktion zum Entfernen einer App aus den Favoriten
    suspend fun removeFavoriteApp(packageName: String) {
        context.dataStore.edit { prefs ->
            val current = prefs[FAVORITE_APPS] ?: emptySet()
            prefs[FAVORITE_APPS] = current - packageName
        }
    }

    // Funktion zum Setzen der gesamten Favoritenliste (z.B. nach Reorder)
    suspend fun setFavoriteApps(packageNames: Set<String>) {
        context.dataStore.edit { prefs ->
            prefs[FAVORITE_APPS] = packageNames
        }
    }

    suspend fun setFontWeightClock(fontWeight: Int) {
        context.dataStore.edit { prefs ->
            prefs[FONT_WEIGHT_CLOCK] = fontWeight
        }
    }

    suspend fun setFontWeightDate(fontWeight: Int) {
        context.dataStore.edit { prefs ->
            prefs[FONT_WEIGHT_DATE] = fontWeight
        }
    }

    suspend fun setFontWeightTexts(fontWeight: Int) {
        context.dataStore.edit { prefs ->
            prefs[FONT_WEIGHT_TEXTS] = fontWeight
        }
    }

    suspend fun setTextAlignment(align: TEXT_ALIGN) {
        context.dataStore.edit { prefs ->
            prefs[TEXT_ALIGNMENT] = align.name
        }
    }

    suspend fun setTextSpacing(spacing: Int) {
        context.dataStore.edit { prefs ->
            prefs[TEXT_SPACING] = spacing
        }
    }

    suspend fun setTextSize(size: Int) {
        context.dataStore.edit { prefs ->
            prefs[TEXT_SIZE] = size
        }
    }

    suspend fun setTextColor(argb: Int) {
        context.dataStore.edit { prefs ->
            prefs[TEXT_COLOR] = argb
        }
    }

    suspend fun setPrimaryColor(argb: Int) {
        context.dataStore.edit { prefs ->
            prefs[PRIMARY_COLOR] = argb
        }
    }

    suspend fun setSecondaryColor(argb: Int) {
        context.dataStore.edit { prefs ->
            prefs[SECONDARY_COLOR] = argb
        }
    }

    // Enhanced color setter functions
    suspend fun setTertiaryColor(argb: Int?) {
        context.dataStore.edit { prefs ->
            if (argb != null) {
                prefs[TERTIARY_COLOR] = argb
            } else {
                prefs.remove(TERTIARY_COLOR)
            }
        }
    }

    suspend fun setGradientDirection(direction: GradientDirection) {
        context.dataStore.edit { prefs ->
            prefs[GRADIENT_DIRECTION] = direction.name
        }
    }

    suspend fun setCustomGradientAngle(angle: Float) {
        context.dataStore.edit { prefs ->
            prefs[CUSTOM_GRADIENT_ANGLE] = angle
        }
    }

    suspend fun setCurrentColorTheme(themeId: String?) {
        context.dataStore.edit { prefs ->
            if (themeId != null) {
                prefs[CURRENT_COLOR_THEME] = themeId
            } else {
                prefs.remove(CURRENT_COLOR_THEME)
            }
        }
    }

    suspend fun setFavoriteColorCombinations(combinations: String) {
        context.dataStore.edit { prefs ->
            prefs[FAVORITE_COLOR_COMBINATIONS] = combinations
        }
    }

    suspend fun setAccessibilityHighContrast(enabled: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[ACCESSIBILITY_HIGH_CONTRAST] = enabled
        }
    }

    suspend fun setAccessibilityColorBlindFriendly(enabled: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[ACCESSIBILITY_COLOR_BLIND_FRIENDLY] = enabled
        }
    }

    suspend fun setAccessibilityAutoBrightness(enabled: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[ACCESSIBILITY_AUTO_BRIGHTNESS] = enabled
        }
    }

    suspend fun setAccessibilityMinContrastRatio(ratio: Float) {
        context.dataStore.edit { prefs ->
            prefs[ACCESSIBILITY_MIN_CONTRAST_RATIO] = ratio
        }
    }

    suspend fun setTimeBasedColorsEnabled(enabled: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[TIME_BASED_COLORS_ENABLED] = enabled
        }
    }

    suspend fun setAnimatedGradientsEnabled(enabled: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[ANIMATED_GRADIENTS_ENABLED] = enabled
        }
    }

    suspend fun setShowFullWeekday(value: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[SHOW_FULL_WEEKDAY] = value
        }
    }

    suspend fun setShowStatusBar(value: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[SHOW_STATUS_BAR] = value
        }
    }

    suspend fun setLockPortraitOrientation(value: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[LOCK_PORTRAIT_ORIENTATION] = value
        }
    }

    suspend fun setSwipeRightApp(app: AppModel) {
        context.dataStore.edit { prefs ->
            prefs[SWIPE_RIGHT_APP] = app.toString()
        }
    }

    suspend fun setSwipeLeftApp(app: AppModel) {
        context.dataStore.edit { prefs ->
            prefs[SWIPE_LEFT_APP] = app.toString()
        }
    }

    // Funktion zum Setzen eines benutzerdefinierten App-Namens
    suspend fun setCustomAppName(packageName: String, customName: String) {
        context.dataStore.edit { prefs ->
            val currentCustomNames = prefs[CUSTOM_APP_NAMES] ?: ""
            val customNamesMap = if (currentCustomNames.isEmpty()) {
                mapOf(packageName to customName)
            } else {
                val map = currentCustomNames.split("|").associate {
                    val parts = it.split(":")
                    if (parts.size == 2) parts[0] to parts[1] else "" to ""
                }.filterKeys { it.isNotEmpty() }.toMutableMap()

                map[packageName] = customName
                map
            }

            // Konvertiere die Map zurück in einen String
            val newCustomNamesString = customNamesMap.entries.joinToString("|") { "${it.key}:${it.value}" }
            prefs[CUSTOM_APP_NAMES] = newCustomNamesString
        }
    }

    // Funktion zum Entfernen eines benutzerdefinierten App-Namens
    suspend fun removeCustomAppName(packageName: String) {
        context.dataStore.edit { prefs ->
            val currentCustomNames = prefs[CUSTOM_APP_NAMES] ?: ""
            if (currentCustomNames.isNotEmpty()) {
                val customNamesMap = currentCustomNames.split("|").associate {
                    val parts = it.split(":")
                    if (parts.size == 2) parts[0] to parts[1] else "" to ""
                }.filterKeys { it.isNotEmpty() }.toMutableMap()

                customNamesMap.remove(packageName)

                // Konvertiere die Map zurück in einen String
                val newCustomNamesString = customNamesMap.entries.joinToString("|") { "${it.key}:${it.value}" }
                prefs[CUSTOM_APP_NAMES] = newCustomNamesString
            }
        }
    }

    // Funktion zum Ausblenden einer App
    suspend fun hideApp(packageName: String) {
        context.dataStore.edit { prefs ->
            val current = prefs[HIDDEN_APPS] ?: emptySet()
            prefs[HIDDEN_APPS] = current + packageName
        }
    }

    // Funktion zum Einblenden einer ausgeblendeten App
    suspend fun unhideApp(packageName: String) {
        context.dataStore.edit { prefs ->
            val current = prefs[HIDDEN_APPS] ?: emptySet()
            prefs[HIDDEN_APPS] = current - packageName
        }
    }

    // Funktion zum Setzen der Einstellung, ob ausgeblendete Apps angezeigt werden sollen
    suspend fun setShowHiddenApps(value: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[SHOW_HIDDEN_APPS] = value
        }
    }

    // Funktion zum Setzen der Einstellung, ob modifizierte Apps hervorgehoben werden sollen
    suspend fun setHighlightModifiedApps(value: Boolean) {
        context.dataStore.edit { prefs ->
            prefs[HIGHLIGHT_MODIFIED_APPS] = value
        }
    }

    // Funktion zum Setzen der bevorzugten Uhr-App
    suspend fun setPreferredClockApp(app: AppModel?) {
        context.dataStore.edit { prefs ->
            if (app != null) {
                prefs[PREFERRED_CLOCK_APP] = app.toString()
            } else {
                prefs.remove(PREFERRED_CLOCK_APP)
            }
        }
    }

    // Funktion zum Setzen der bevorzugten Kalender-App
    suspend fun setPreferredCalendarApp(app: AppModel?) {
        context.dataStore.edit { prefs ->
            if (app != null) {
                prefs[PREFERRED_CALENDAR_APP] = app.toString()
            } else {
                prefs.remove(PREFERRED_CALENDAR_APP)
            }
        }
    }

    // Funktion zum Prüfen, ob eine App ausgeblendet ist
    fun isAppHidden(packageName: String, hiddenApps: Set<String>): Boolean {
        return hiddenApps.contains(packageName)
    }
}
