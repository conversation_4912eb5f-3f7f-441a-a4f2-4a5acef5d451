package com.kevinnovation.detoxlauncher.navigation

/**
 * Navigation destinations for type-safe navigation
 */
sealed class NavigationDestination(val route: String) {
    object Home : NavigationDestination("home")
    object Drawer : NavigationDestination("drawer/{drawerMode}") {
        fun createRoute(drawerMode: String) = "drawer/$drawerMode"
    }
    object Settings : NavigationDestination("settings")
}

/**
 * Drawer modes for navigation arguments
 */
enum class DrawerMode {
    OPEN,
    CHOOSE_SWIPE_LEFT,
    CHOOSE_SWIPE_RIGHT,
    CHOOSE_CLOCK_APP,
    CHOOSE_CALENDAR_APP;

    companion object {
        fun fromString(value: String?): DrawerMode {
            return when (value) {
                "CHOOSE_SWIPE_LEFT" -> CHOOSE_SWIPE_LEFT
                "CHOOSE_SWIPE_RIGHT" -> CHOOSE_SWIPE_RIGHT
                "CHOOSE_CLOCK_APP" -> CHOOSE_CLOCK_APP
                "CHOOSE_CALENDAR_APP" -> CHOOSE_CALENDAR_APP
                else -> OPEN
            }
        }
    }
}

/**
 * Navigation arguments keys
 */
object NavigationArgs {
    const val DRAWER_MODE = "drawerMode"
}
