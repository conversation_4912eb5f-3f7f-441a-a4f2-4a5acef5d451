package com.kevinnovation.detoxlauncher.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import com.kevinnovation.detoxlauncher.utils.AppUtils
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import kotlinx.coroutines.launch

@Composable
fun DetoxClock(viewModel: MainViewModel) {

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val preferencesManager = remember { PreferencesManager(context) }

    val currentTime by viewModel.currentTime.collectAsState()
    val currentDate by viewModel.currentDate.collectAsState()
    val batteryLevel by viewModel.batteryLevel.collectAsState()
    val fontWeightClock by viewModel.fontWeightClock
    val fontWeightDate by viewModel.fontWeightDate
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val tertiaryColor by viewModel.tertiaryColor.collectAsState()
    val textColor by viewModel.textColor.collectAsState()

    // State for app selection dialogs
    var showClockAppSelection by remember { mutableStateOf(false) }
    var showCalendarAppSelection by remember { mutableStateOf(false) }
    var clockApps by remember { mutableStateOf<List<AppModel>>(emptyList()) }
    var calendarApps by remember { mutableStateOf<List<AppModel>>(emptyList()) }

    // Obere Leiste (Uhrzeit, Datum, Akku)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier
                .clickable {
                    scope.launch {
                        AppUtils.openDefaultClockApp(context) { apps ->
                            clockApps = apps
                            showClockAppSelection = true
                        }
                    }
                },
            text = currentTime,
            style = TextStyle(
                brush = Brush.horizontalGradient(
                    colors = listOf(primaryColor, secondaryColor)
                ),
                fontSize = 90.sp,
                fontWeight = fontWeightClock,
//                shadow = shadowStyle
            ),
            color = Color.Black
        )
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier
                .clickable {
                    scope.launch {
                        AppUtils.openDefaultCalendarApp(context) { apps ->
                            calendarApps = apps
                            showCalendarAppSelection = true
                        }
                    }
                },
            text = "$currentDate • $batteryLevel",
            style = TextStyle(
                brush = Brush.linearGradient(
                    colors = listOf(primaryColor, secondaryColor)
                ),
                fontSize = 20.sp,
                fontWeight = fontWeightDate
            )

        )
    }

    // Clock app selection dialog
    if (showClockAppSelection) {
        AppSelectionDialog(
            title = stringResource(R.string.settings_select_clock_app),
            apps = clockApps,
            textColor = textColor,
            onAppSelected = { selectedApp ->
                scope.launch {
                    preferencesManager.setPreferredClockApp(selectedApp)
                    AppUtils.startApp(selectedApp, context)
                    showClockAppSelection = false
                }
            },
            onDismiss = {
                showClockAppSelection = false
            }
        )
    }

    // Calendar app selection dialog
    if (showCalendarAppSelection) {
        AppSelectionDialog(
            title = stringResource(R.string.settings_select_calendar_app),
            apps = calendarApps,
            textColor = textColor,
            onAppSelected = { selectedApp ->
                scope.launch {
                    preferencesManager.setPreferredCalendarApp(selectedApp)
                    AppUtils.startApp(selectedApp, context)
                    showCalendarAppSelection = false
                }
            },
            onDismiss = {
                showCalendarAppSelection = false
            }
        )
    }
}

