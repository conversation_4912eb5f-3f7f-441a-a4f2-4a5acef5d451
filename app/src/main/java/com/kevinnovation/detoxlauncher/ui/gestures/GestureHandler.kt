package com.kevinnovation.detoxlauncher.ui.gestures

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.awaitTouchSlopOrCancellation
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.drag
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerInputScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.util.VelocityTracker
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlin.math.abs
import kotlin.math.absoluteValue
import kotlin.math.sqrt

/**
 * Gesture configuration for swipe detection
 */
data class GestureConfig(
    val swipeThreshold: Float = 120f,  // Optimized threshold for responsive swipes
    val velocityThreshold: Float = 400f,  // Balanced velocity requirement
    val timeoutMs: Long = 300L,  // Reduced for faster response
    val debounceMs: Long = 150L,  // Shorter debounce for better responsiveness
    val enableLogging: Boolean = false,
    val minSwipeDistance: Float = 80f,  // Minimum distance for a valid swipe
    val maxSwipeAngle: Float = 45f  // Maximum angle deviation for directional swipes
)

/**
 * Gesture events
 */
sealed class GestureEvent {
    object SwipeUp : GestureEvent()
    object SwipeDown : GestureEvent()
    object SwipeLeft : GestureEvent()
    object SwipeRight : GestureEvent()
    object LongPress : GestureEvent()
    object Tap : GestureEvent()
}

/**
 * Gesture callbacks
 */
data class GestureCallbacks(
    val onSwipeUp: ((isLeft: Boolean) -> Unit)? = null,
    val onSwipeDown: (() -> Unit)? = null,
    val onSwipeLeft: (() -> Unit)? = null,
    val onSwipeRight: (() -> Unit)? = null,
    val onLongPress: (() -> Unit)? = null,
    val onTap: (() -> Unit)? = null
)

/**
 * Enhanced gesture handler with improved performance and reliability
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GestureHandler(
    modifier: Modifier = Modifier,
    config: GestureConfig = GestureConfig(),
    callbacks: GestureCallbacks,
    content: @Composable () -> Unit
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }

    var gestureStartPosition by remember { mutableStateOf(Offset.Zero) }
    var lastGestureTime by remember { mutableStateOf(0L) }
    var gestureProcessed by remember { mutableStateOf(false) }

    androidx.compose.foundation.layout.Box(
        modifier = modifier
            .fillMaxSize()
            .combinedClickable(
                onClick = {
                    callbacks.onTap?.invoke()
                    if (config.enableLogging) {
                        Log.d("GestureHandler", "Tap detected")
                    }
                },
                onLongClick = {
                    callbacks.onLongPress?.invoke()
                    if (config.enableLogging) {
                        Log.d("GestureHandler", "Long press detected")
                    }
                }
            )
            .pointerInput(Unit) {
                val velocityTracker = VelocityTracker()

                detectDragGestures(
                    onDragStart = { offset ->
                        gestureStartPosition = offset
                        gestureProcessed = false
                        velocityTracker.resetTracking()
                        velocityTracker.addPosition(System.currentTimeMillis(), offset)

                        if (config.enableLogging) {
                            Log.d("GestureHandler", "Gesture started at: $offset")
                        }
                    },
                    onDragEnd = {
                        // Reset tracking when gesture ends
                        velocityTracker.resetTracking()
                    },
                    onDrag = { change, dragAmount ->
                        // Add current position to velocity tracker
                        velocityTracker.addPosition(change.uptimeMillis, change.position)

                        // Only process if we haven't already processed this gesture
                        if (!gestureProcessed) {
                            val currentTime = System.currentTimeMillis()
                            val totalDrag = change.position - gestureStartPosition
                            val distance = sqrt(totalDrag.x * totalDrag.x + totalDrag.y * totalDrag.y)

                            // Check minimum distance first (early exit for performance)
                            if (distance > config.minSwipeDistance &&
                                currentTime - lastGestureTime > config.debounceMs) {

                                // Calculate velocity only when needed
                                val velocity = velocityTracker.calculateVelocity()
                                val velocityMagnitude = sqrt(velocity.x * velocity.x + velocity.y * velocity.y)

                                // Check if this meets our swipe criteria
                                val meetsDistanceThreshold = distance > config.swipeThreshold
                                val meetsVelocityThreshold = velocityMagnitude > config.velocityThreshold

                                // Process gesture if either threshold is met (more responsive)
                                if (meetsDistanceThreshold || meetsVelocityThreshold) {
                                    val direction = determineSwipeDirection(totalDrag)
                                    processGesture(direction, gestureStartPosition, screenWidth, callbacks, config)

                                    gestureProcessed = true
                                    lastGestureTime = currentTime

                                    // Consume the change to prevent further processing
                                    change.consume()
                                }
                            }
                        }
                    }
                )
            }
    ) {
        content()
    }
}

/**
 * Process the detected gesture
 */
private fun processGesture(
    direction: SwipeDirection,
    startPosition: Offset,
    screenWidth: Float,
    callbacks: GestureCallbacks,
    config: GestureConfig
) {
    when (direction) {
        SwipeDirection.UP -> {
            val isLeft = startPosition.x < screenWidth / 2
            // Update PreferencesManager.IS_LEFT for compatibility
            com.kevinnovation.detoxlauncher.data.PreferencesManager.IS_LEFT = isLeft

            callbacks.onSwipeUp?.invoke(isLeft)
            if (config.enableLogging) {
                Log.d("GestureHandler", "Swipe up detected (left side: $isLeft)")
            }
        }
        SwipeDirection.DOWN -> {
            callbacks.onSwipeDown?.invoke()
            if (config.enableLogging) {
                Log.d("GestureHandler", "Swipe down detected")
            }
        }
        SwipeDirection.LEFT -> {
            callbacks.onSwipeLeft?.invoke()
            if (config.enableLogging) {
                Log.d("GestureHandler", "Swipe left detected")
            }
        }
        SwipeDirection.RIGHT -> {
            callbacks.onSwipeRight?.invoke()
            if (config.enableLogging) {
                Log.d("GestureHandler", "Swipe right detected")
            }
        }
    }
}

/**
 * Swipe direction enum
 */
private enum class SwipeDirection {
    UP, DOWN, LEFT, RIGHT
}

/**
 * Determine swipe direction from offset with improved accuracy
 */
private fun determineSwipeDirection(offset: Offset): SwipeDirection {
    val horizontalDistance = abs(offset.x)
    val verticalDistance = abs(offset.y)

    // Require a minimum distance ratio to avoid diagonal swipes being misinterpreted
    val dominanceRatio = 1.5f

    return when {
        horizontalDistance > verticalDistance * dominanceRatio -> {
            if (offset.x > 0) SwipeDirection.RIGHT else SwipeDirection.LEFT
        }
        verticalDistance > horizontalDistance * dominanceRatio -> {
            if (offset.y > 0) SwipeDirection.DOWN else SwipeDirection.UP
        }
        else -> {
            // For ambiguous cases, use the larger component
            if (horizontalDistance > verticalDistance) {
                if (offset.x > 0) SwipeDirection.RIGHT else SwipeDirection.LEFT
            } else {
                if (offset.y > 0) SwipeDirection.DOWN else SwipeDirection.UP
            }
        }
    }
}
