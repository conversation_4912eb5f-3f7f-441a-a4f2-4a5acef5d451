package com.kevinnovation.detoxlauncher.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Enhanced color palette for clock themes
object ClockColors {
    // Sunset theme
    val SunsetOrange = Color(0xFFFF6B35)
    val SunsetPink = Color(0xFFFF8E9B)
    val SunsetRed = Color(0xFFFF4757)
    val SunsetYellow = Color(0xFFFFA726)

    // Ocean theme
    val OceanBlue = Color(0xFF0077BE)
    val OceanTeal = Color(0xFF00BCD4)
    val OceanDeep = Color(0xFF1565C0)
    val OceanLight = Color(0xFF4FC3F7)

    // Neon theme
    val NeonGreen = Color(0xFF39FF14)
    val NeonPink = Color(0xFFFF073A)
    val NeonBlue = Color(0xFF0080FF)
    val NeonPurple = Color(0xFF8A2BE2)

    // Pastel theme
    val PastelPink = Color(0xFFFFB3BA)
    val PastelBlue = Color(0xFFBAE1FF)
    val PastelGreen = Color(0xFFBAFFB3)
    val PastelYellow = Color(0xFFFFFFBA)

    // Monochrome theme
    val MonoWhite = Color(0xFFFFFFFF)
    val MonoGray = Color(0xFF808080)
    val MonoBlack = Color(0xFF000000)
    val MonoSilver = Color(0xFFC0C0C0)

    // Nature theme
    val NatureGreen = Color(0xFF4CAF50)
    val NatureBrown = Color(0xFF8D6E63)
    val NatureBlue = Color(0xFF2196F3)
    val NatureYellow = Color(0xFFFFEB3B)

    // Fire theme
    val FireRed = Color(0xFFD32F2F)
    val FireOrange = Color(0xFFFF5722)
    val FireYellow = Color(0xFFFFC107)
    val FireDeep = Color(0xFFB71C1C)

    // Ice theme
    val IceBlue = Color(0xFFE3F2FD)
    val IceCyan = Color(0xFF00E5FF)
    val IceWhite = Color(0xFFF8F9FA)
    val IceSilver = Color(0xFFCFD8DC)

    // Galaxy theme
    val GalaxyPurple = Color(0xFF673AB7)
    val GalaxyBlue = Color(0xFF3F51B5)
    val GalaxyPink = Color(0xFFE91E63)
    val GalaxyDeep = Color(0xFF1A237E)

    // Autumn theme
    val AutumnOrange = Color(0xFFFF9800)
    val AutumnRed = Color(0xFFD84315)
    val AutumnYellow = Color(0xFFFDD835)
    val AutumnBrown = Color(0xFF5D4037)
}