package com.kevinnovation.detoxlauncher.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.data.*
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

@Composable
fun ThemeSelector(
    viewModel: MainViewModel,
    selectedCategory: ThemeCategory,
    onCategorySelected: (ThemeCategory) -> Unit,
    onThemeSelected: (ColorTheme) -> Unit,
    modifier: Modifier = Modifier
) {
    val textColor by viewModel.textColor.collectAsState()
    val favoriteThemes by viewModel.favoriteColorCombinations.collectAsState()
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        // Category selector
        CategorySelector(
            selectedCategory = selectedCategory,
            onCategorySelected = onCategorySelected,
            textColor = textColor
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Theme grid
        ThemeGrid(
            themes = getThemesForCategory(selectedCategory, favoriteThemes),
            onThemeSelected = onThemeSelected,
            onFavoriteToggle = { theme, isFavorite ->
                viewModel.toggleFavoriteTheme(theme, isFavorite)
            },
            favoriteThemes = favoriteThemes
        )
    }
}

@Composable
fun CategorySelector(
    selectedCategory: ThemeCategory,
    onCategorySelected: (ThemeCategory) -> Unit,
    textColor: Color
) {
    val categories = listOf(
        ThemeCategory.POPULAR,
        ThemeCategory.NATURE,
        ThemeCategory.NEON,
        ThemeCategory.PASTEL,
        ThemeCategory.MONOCHROME,
        ThemeCategory.FIRE,
        ThemeCategory.ICE,
        ThemeCategory.GALAXY,
        ThemeCategory.FAVORITES
    )
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 8.dp)
    ) {
        items(categories) { category ->
            FilterChip(
                onClick = { onCategorySelected(category) },
                label = {
                    Text(
                        text = category.name.lowercase().replaceFirstChar { it.uppercase() },
                        fontSize = 12.sp
                    )
                },
                selected = selectedCategory == category,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = textColor.copy(alpha = 0.2f),
                    selectedLabelColor = textColor
                )
            )
        }
    }
}

@Composable
fun ThemeGrid(
    themes: List<ColorTheme>,
    onThemeSelected: (ColorTheme) -> Unit,
    onFavoriteToggle: (ColorTheme, Boolean) -> Unit,
    favoriteThemes: List<ColorTheme>
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.height(300.dp)
    ) {
        items(themes) { theme ->
            ThemeCard(
                theme = theme,
                onThemeSelected = { onThemeSelected(theme) },
                onFavoriteToggle = { isFavorite ->
                    onFavoriteToggle(theme, isFavorite)
                },
                isFavorite = favoriteThemes.any { it.id == theme.id }
            )
        }
    }
}

@Composable
fun ThemeCard(
    theme: ColorTheme,
    onThemeSelected: () -> Unit,
    onFavoriteToggle: (Boolean) -> Unit,
    isFavorite: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp)
            .clickable { onThemeSelected() },
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Gradient preview
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(theme.createBrush())
            )
            
            // Overlay with theme info
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
            
            // Theme name
            Text(
                text = theme.name,
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(8.dp)
            )
            
            // Favorite button
            IconButton(
                onClick = { onFavoriteToggle(!isFavorite) },
                modifier = Modifier.align(Alignment.TopEnd)
            ) {
                Icon(
                    imageVector = if (isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                    contentDescription = if (isFavorite) "Remove from favorites" else "Add to favorites",
                    tint = if (isFavorite) Color.Red else Color.White
                )
            }
        }
    }
}

@Composable
fun GradientDirectionSelector(
    selectedDirection: GradientDirection,
    onDirectionSelected: (GradientDirection) -> Unit,
    textColor: Color
) {
    Column {
        Text(
            text = "Gradient Direction",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            modifier = Modifier.padding(8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 8.dp)
        ) {
            items(GradientDirection.values()) { direction ->
                FilterChip(
                    onClick = { onDirectionSelected(direction) },
                    label = {
                        Text(
                            text = when (direction) {
                                GradientDirection.HORIZONTAL -> "→"
                                GradientDirection.VERTICAL -> "↓"
                                GradientDirection.DIAGONAL_TL_BR -> "↘"
                                GradientDirection.DIAGONAL_TR_BL -> "↙"
                                GradientDirection.RADIAL -> "◉"
                            },
                            fontSize = 16.sp
                        )
                    },
                    selected = selectedDirection == direction,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = textColor.copy(alpha = 0.2f),
                        selectedLabelColor = textColor
                    )
                )
            }
        }
    }
}

@Composable
fun LivePreview(
    primaryColor: Color,
    secondaryColor: Color,
    tertiaryColor: Color?,
    gradientDirection: GradientDirection,
    modifier: Modifier = Modifier
) {
    val theme = ColorTheme(
        id = "preview",
        name = "Preview",
        primaryColor = primaryColor,
        secondaryColor = secondaryColor,
        tertiaryColor = tertiaryColor,
        gradientDirection = gradientDirection
    )
    
    Column(
        modifier = modifier.padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Live Preview",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // Clock preview
        Text(
            text = "12:34",
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold,
            style = androidx.compose.ui.text.TextStyle(
                brush = theme.createBrush()
            ),
            modifier = Modifier
                .padding(16.dp)
                .background(
                    Color.Black.copy(alpha = 0.1f),
                    RoundedCornerShape(8.dp)
                )
                .padding(16.dp)
        )
    }
}

private fun getThemesForCategory(category: ThemeCategory, favoriteThemes: List<ColorTheme>): List<ColorTheme> {
    return when (category) {
        ThemeCategory.FAVORITES -> favoriteThemes
        ThemeCategory.POPULAR -> ColorPresets.popularThemes
        ThemeCategory.NATURE -> ColorPresets.natureThemes
        ThemeCategory.NEON -> ColorPresets.neonThemes
        ThemeCategory.PASTEL -> ColorPresets.pastelThemes
        ThemeCategory.MONOCHROME -> ColorPresets.monochromeThemes
        ThemeCategory.FIRE -> ColorPresets.fireThemes
        ThemeCategory.ICE -> ColorPresets.iceThemes
        ThemeCategory.GALAXY -> ColorPresets.galaxyThemes
        ThemeCategory.SUNSET -> ColorPresets.popularThemes.filter { it.name.contains("Sunset") }
        ThemeCategory.OCEAN -> ColorPresets.popularThemes.filter { it.name.contains("Ocean") }
        ThemeCategory.AUTUMN -> ColorPresets.natureThemes.filter { it.name.contains("Autumn") }
        ThemeCategory.CUSTOM -> emptyList()
    }
}
