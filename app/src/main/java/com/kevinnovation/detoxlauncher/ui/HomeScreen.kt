package com.kevinnovation.detoxlauncher.ui

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.awaitTouchSlopOrCancellation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsIgnoringVisibility
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Clear
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerInputScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.toSize
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import com.kevinnovation.detoxlauncher.ui.gestures.GestureCallbacks
import com.kevinnovation.detoxlauncher.ui.gestures.GestureConfig
import com.kevinnovation.detoxlauncher.ui.gestures.GestureHandler
import com.kevinnovation.detoxlauncher.utils.AppUtils
import com.kevinnovation.detoxlauncher.utils.PerformanceMonitor
import com.kevinnovation.detoxlauncher.utils.rememberManagedCoroutineScope
import com.kevinnovation.detoxlauncher.utils.shadowStyle
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import com.kevinnovation.detoxlauncher.viewmodel.TEXT_ALIGN
import kotlinx.coroutines.launch
import rememberGlobalGradient
import kotlin.math.absoluteValue

@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun HomeScreen(
    viewModel: MainViewModel,
    onSwipeLeft: () -> Unit,
    onSwipeRight: () -> Unit,
    onSwipeUp: (isLeft: Boolean) -> Unit,
    onSwipeDown: () -> Unit,
    onSettingsRequested: () -> Unit,
    onRemoveFromFavorites: suspend (String) -> Unit
) {
    val context = LocalContext.current
    val managedScope = rememberManagedCoroutineScope()

    // Performance monitoring
    PerformanceMonitor(
        screenName = "HomeScreen",
        onPerformanceIssue = { issue ->
            Log.w("HomeScreen", "Performance issue: $issue")
        }
    )

    var containerSize by remember { mutableStateOf(Size.Zero) }

    // Collect state efficiently
    val favoriteApps by viewModel.favoriteApps.collectAsState()
    val textSize by viewModel.textSize.collectAsState()
    val fontWeightClock by viewModel.fontWeightClock
    val fontWeightDate by viewModel.fontWeightDate
    val fontWeightTexts by viewModel.fontWeightTexts
    val textSpacing by viewModel.textSpacing.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val textAlign by viewModel.textAlignment.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val currentTime by viewModel.currentTime.collectAsState()
    val currentDate by viewModel.currentDate.collectAsState()
    val batteryLevel by viewModel.batteryLevel.collectAsState()
    val isLeft by viewModel.isLeft.collectAsState()

    // State für Kontextmenü
    var showMenuForApp by remember { mutableStateOf<AppModel?>(null) }

    // Gesture configuration - optimized for smooth swipe experience
    val gestureConfig = remember {
        GestureConfig(
            swipeThreshold = 100f,  // Lower threshold for more responsive swipes
            velocityThreshold = 350f,  // Balanced velocity for natural gestures
            debounceMs = 120L,  // Shorter debounce for better responsiveness
            minSwipeDistance = 60f,  // Lower minimum for easier swipes
            enableLogging = false  // Disable for production performance
        )
    }

    val gestureCallbacks = remember {
        GestureCallbacks(
            onSwipeUp = { isLeftSide -> onSwipeUp(isLeftSide) },
            onSwipeDown = onSwipeDown,
            onSwipeLeft = onSwipeLeft,
            onSwipeRight = onSwipeRight,
            onLongPress = onSettingsRequested
        )
    }
    // Use new gesture handler
    GestureHandler(
        modifier = Modifier
            .fillMaxSize()
            .padding(WindowInsets.systemBarsIgnoringVisibility.asPaddingValues())
            .padding(horizontal = 16.dp),
        config = gestureConfig,
        callbacks = gestureCallbacks
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
        val globalGradient = rememberGlobalGradient(
            colors = listOf(
                textColor,
                textColor,
//                Color(206, 245, 255, 255)
            ),
            totalSize = containerSize
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 40.dp)
        ) {
            DetoxClock(viewModel = viewModel)
            SetDefaultLauncherScreen(viewModel = viewModel, context = context)
            Row(modifier = Modifier
                .fillMaxSize()
                .onGloballyPositioned { coords ->
                    Log.d("kevinx", "choords => ${coords.size}")
                    containerSize = coords.size.toSize()
                }) {
                // Favoriten-Apps in der Mitte
                Column(
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 25.dp, end = 12.dp)
                ) {
                    if (favoriteApps.isEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.Transparent)
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Surface(
                                    shape = RoundedCornerShape(25.dp),
                                    color = Color.Black.copy(alpha = 0.5f),
                                    modifier = Modifier
                                        .width(300.dp)
                                        .wrapContentHeight()
                                ) {
                                    Box(contentAlignment = Alignment.Center) {
                                        Text(
                                            modifier = Modifier.padding(15.dp),
                                            text = stringResource(id = R.string.homescreen_welcome),
                                            color = textColor,
                                            fontSize = 20.sp,
                                            fontWeight = fontWeightTexts,
                                            textAlign = TextAlign.Center
                                        )
                                    }
                                }
                            }
                        }
                    } else {
                        favoriteApps.forEach { app ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color.Transparent)
                                    .combinedClickable(
                                        onClick = {
                                           AppUtils.startApp(app, context)
                                        },
                                        onLongClick = {
                                            showMenuForApp = app
                                        }
                                    )
                                    .padding(8.dp, top = textSpacing.dp, bottom = textSpacing.dp)
                            ) {
                                // DropdownMenu für Add/Remove Favorites
                                if (showMenuForApp?.equals(app) == true) {
                                    DropdownMenu(
                                        expanded = true,
                                        onDismissRequest = { showMenuForApp = null },
                                        modifier = Modifier.background(
                                            brush = Brush.linearGradient(
                                                colors = listOf(Color.Red, Color.Magenta)
                                            )
                                        )
                                    ) {
                                        DropdownMenuItem(
                                            leadingIcon = {
                                                Icon(
                                                    Icons.Outlined.Clear,
                                                    contentDescription = null,
                                                    tint = Color.White
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(id = R.string.global_remove_from_favorites),
                                                    fontWeight = FontWeight.Normal,
                                                    color = Color.White,
                                                    fontSize = maxOf(textSize - 15, 15).sp

                                                )
                                            },
                                            onClick = {
                                                managedScope.launch {
                                                    onRemoveFromFavorites(app.packageName)
                                                    showMenuForApp = null
                                                }
                                            }
                                        )
                                    }
                                }
                                val alignment = when(textAlign) {
                                    TEXT_ALIGN.LEFT -> { Alignment.CenterStart }
                                    TEXT_ALIGN.CENTER -> { Alignment.Center }
                                    TEXT_ALIGN.RIGHT -> { Alignment.CenterEnd }
                                }
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentAlignment = alignment
                                ) {
//                                    GlobalGradientText(
//                                        text = app.getDisplayName(),
//                                        globalGradient = globalGradient
//                                    )
                                    Text(
//                                        globalGradient = globalGradient,
                                        modifier = Modifier.wrapContentWidth(),
//                                        textAlign = TextAlign.End,
                                        text = app.getDisplayName(),
                                        style = TextStyle(
//                                            brush = Brush.horizontalGradient(
//                                                colors = listOf(Color.White, Color.Cyan.copy(alpha = 0.5f)),
//                                            ),
                                            color = textColor,
                                            fontSize = textSize.sp,
                                            fontWeight = fontWeightTexts,
                                            shadow = shadowStyle
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        } // End of Box
    } // End of GestureHandler
}

