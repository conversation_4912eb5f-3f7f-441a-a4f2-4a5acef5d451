package com.kevinnovation.detoxlauncher

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.google.android.gms.ads.MobileAds
import com.kevinnovation.detoxlauncher.navigation.NavigationGraph
import com.kevinnovation.detoxlauncher.ui.theme.DetoxLauncherTheme
import com.kevinnovation.detoxlauncher.utils.AutoMemoryManager
import com.kevinnovation.detoxlauncher.utils.ConsentManager
import com.kevinnovation.detoxlauncher.utils.MemoryManager
import com.kevinnovation.detoxlauncher.utils.PerformanceMonitor
import com.kevinnovation.detoxlauncher.utils.PerformanceUtils
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModelFactory
import com.kevinnovation.detoxlauncher.viewmodel.NavigationViewModel

class MainActivity : ComponentActivity() {
    private lateinit var consentManager: ConsentManager

    // ViewModels with improved lifecycle management
    private val mainViewModel: MainViewModel by viewModels {
        MainViewModelFactory(application, consentManager)
    }

    // Navigation ViewModel for handling home button navigation
    private lateinit var navigationViewModel: NavigationViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize performance monitoring
        PerformanceUtils.logMemoryUsage("MainActivity.onCreate")

        // Initialize consent manager
        initConsentManager(this)

        // Enable edge-to-edge display
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT)
        )

        setContent {
            DetoxLauncherTheme {
                val context = LocalContext.current
                val navController = rememberNavController()
                navigationViewModel = viewModel()

                // Performance monitoring
                PerformanceMonitor(
                    screenName = "MainActivity",
                    onPerformanceIssue = { issue ->
                        Log.w("Performance", issue)
                    }
                )

                // Memory management
                AutoMemoryManager(
                    context = context,
                    onLowMemory = {
                        Log.w("Memory", "Low memory detected - cleaning up")
                        MemoryManager.forceGCIfNeeded(context)
                    }
                )

                // UI State management
                val showStatusBar by mainViewModel.showStatusBar.collectAsState()
                val lockPortraitOrientation by mainViewModel.lockPortraitOrientation.collectAsState()
                val isLoading by mainViewModel.isLoading.collectAsState()

                // Status bar management
                val view = LocalView.current
                LaunchedEffect(showStatusBar) {
                    updateStatusBarView(view, showStatusBar)
                }

                // Orientation management
                LaunchedEffect(lockPortraitOrientation) {
                    requestedOrientation = if (lockPortraitOrientation) {
                        ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    } else {
                        ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                    }
                }

                // Loading state
                if (isLoading) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        CustomLoadingIndicator(
                            viewModel = mainViewModel,
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                } else {
                    // New Navigation System
                    NavigationGraph(
                        navController = navController,
                        mainViewModel = mainViewModel,
                        navigationViewModel = navigationViewModel
                    )
                }


            }
        }
    }

    private fun updateStatusBarView(view: View, showStatusBar: Boolean) {
        val window = (view.context as Activity).window
        val controller = WindowCompat.getInsetsController(window, window.decorView)
        Log.d("kevinx", "LaunchedEffect Coroutine gestartet (showStatusBar=$showStatusBar)")
        // Statusleiste ausblenden
        if (!showStatusBar) {
            controller.hide(WindowInsetsCompat.Type.statusBars())
        } else {
            controller.show(WindowInsetsCompat.Type.statusBars())
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.d("Lifecycle", "Saving instance state")
        PerformanceUtils.logMemoryUsage("onSaveInstanceState")
    }

    override fun onResume() {
        super.onResume()
        Log.d("Lifecycle", "MainActivity.onResume()")
        PerformanceUtils.logMemoryUsage("onResume")

        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT)
        )

        // Check memory status on resume
        if (MemoryManager.isLowMemory(this)) {
            Log.w("Memory", "Low memory detected on resume")
            MemoryManager.forceGCIfNeeded(this)
        }

        // Check launcher status when app resumes
        mainViewModel.refreshLauncherStatus()
    }

    override fun onPause() {
        super.onPause()
        Log.d("Lifecycle", "MainActivity.onPause()")

        // Clean up resources when pausing
        MemoryManager.forceGCIfNeeded(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("Lifecycle", "MainActivity.onDestroy()")

        // Cancel all coroutine scopes to prevent memory leaks
        MemoryManager.cancelAllScopes()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Log.d("Lifecycle", "onNewIntent called with action: ${intent?.action}")

        // Handle home button press - navigate to home screen
        if (intent?.action == Intent.ACTION_MAIN &&
            intent.hasCategory(Intent.CATEGORY_HOME)) {
            Log.d("Navigation", "Home button pressed - navigating to home")

            // Navigate to home screen if navigationViewModel is initialized
            if (::navigationViewModel.isInitialized) {
                navigationViewModel.forceNavigateToHome()
            }

            // Check launcher status when home button is pressed
            mainViewModel.refreshLauncherStatus()
        }
    }

    private fun initConsentManager(activity: MainActivity) {
        // DSGVO Consent-Dialog anzeigen und erst danach AdMob starten
        consentManager = ConsentManager(activity)
        consentManager.checkConsentAndShowForm(activity) {
            if (consentManager.canRequestAds()) {
                MobileAds.initialize(activity) // AdMob erst nach Zustimmung starten
            }
        }
    }

    private fun expandNotificationsPanel(context: Context) {
        try {
            // Use reflection for older Android versions
            val statusBarService = context.getSystemService("statusbar")
            val statusBarManager = Class.forName("android.app.StatusBarManager")
            val expandMethod = statusBarManager.getMethod("expandNotificationsPanel")
            Log.d("StatusBar", "openStatusBar")
            expandMethod.invoke(statusBarService)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}

/**
 * Custom loading indicator with gradient colors from primary and secondary colors
 */
@Composable
fun CustomLoadingIndicator(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    // Animation for rotation
    val infiniteTransition = rememberInfiniteTransition(label = "loading_rotation")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1200, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    // Animation for gradient shift
    val gradientShift by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "gradient_shift"
    )

    Box(
        modifier = modifier.size(64.dp)
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .rotate(rotation)
        ) {
            val strokeWidth = 6.dp.toPx()
            val radius = (size.minDimension - strokeWidth) / 2

            // Create animated gradient
            val colors = listOf(
                primaryColor,
                secondaryColor,
                primaryColor.copy(alpha = 0.3f),
                secondaryColor.copy(alpha = 0.8f)
            )

            val brush = Brush.sweepGradient(
                colors = colors,
                center = center
            )

            // Draw the loading circle
            drawCircle(
                brush = brush,
                radius = radius,
                center = center,
                style = Stroke(
                    width = strokeWidth,
                    cap = StrokeCap.Round
                )
            )
        }
    }
}