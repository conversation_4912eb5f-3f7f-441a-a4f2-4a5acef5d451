package com.kevinnovation.detoxlauncher.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.kevinnovation.detoxlauncher.navigation.DrawerMode
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Navigation events for type-safe navigation
 */
sealed class NavigationEvent {
    object NavigateToHome : NavigationEvent()
    data class NavigateToDrawer(val drawerMode: DrawerMode) : NavigationEvent()
    object NavigateToSettings : NavigationEvent()
    object NavigateBack : NavigationEvent()
}

/**
 * ViewModel responsible for navigation state and events
 * Separates navigation logic from business logic
 */
class NavigationViewModel : ViewModel() {
    
    private val _navigationEvent = MutableStateFlow<NavigationEvent?>(null)
    val navigationEvent: StateFlow<NavigationEvent?> = _navigationEvent.asStateFlow()
    
    private val _isNavigating = MutableStateFlow(false)
    val isNavigating: StateFlow<Boolean> = _isNavigating.asStateFlow()
    
    private var lastNavigationTime = 0L
    private val NAVIGATION_DEBOUNCE_MS = 300L // Prevent rapid navigation
    
    /**
     * Navigate to home screen
     */
    fun navigateToHome() {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating to Home")
            _navigationEvent.value = NavigationEvent.NavigateToHome
            updateNavigationTime()
        }
    }
    
    /**
     * Navigate to drawer with specific mode
     */
    fun navigateToDrawer(drawerMode: DrawerMode) {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating to Drawer with mode: $drawerMode")
            _navigationEvent.value = NavigationEvent.NavigateToDrawer(drawerMode)
            updateNavigationTime()
        }
    }
    
    /**
     * Navigate to settings screen
     */
    fun navigateToSettings() {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating to Settings")
            _navigationEvent.value = NavigationEvent.NavigateToSettings
            updateNavigationTime()
        }
    }
    
    /**
     * Navigate back
     */
    fun navigateBack() {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating back")
            _navigationEvent.value = NavigationEvent.NavigateBack
            updateNavigationTime()
        }
    }

    /**
     * Navigate to drawer for clock app selection
     */
    fun navigateToClockAppSelection() {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating to Clock App Selection")
            _navigationEvent.value = NavigationEvent.NavigateToDrawer(DrawerMode.CHOOSE_CLOCK_APP)
            updateNavigationTime()
        }
    }

    /**
     * Navigate to drawer for calendar app selection
     */
    fun navigateToCalendarAppSelection() {
        if (canNavigate()) {
            Log.d("NavigationViewModel", "Navigating to Calendar App Selection")
            _navigationEvent.value = NavigationEvent.NavigateToDrawer(DrawerMode.CHOOSE_CALENDAR_APP)
            updateNavigationTime()
        }
    }
    
    /**
     * Clear the current navigation event after it's been handled
     */
    fun clearNavigationEvent() {
        _navigationEvent.value = null
        _isNavigating.value = false
    }
    
    /**
     * Check if navigation is allowed (debouncing)
     */
    private fun canNavigate(): Boolean {
        val currentTime = System.currentTimeMillis()
        return currentTime - lastNavigationTime >= NAVIGATION_DEBOUNCE_MS
    }
    
    /**
     * Update navigation time and set navigating state
     */
    private fun updateNavigationTime() {
        lastNavigationTime = System.currentTimeMillis()
        _isNavigating.value = true
    }
    
    /**
     * Force navigation (bypass debouncing) - use carefully
     */
    fun forceNavigateToHome() {
        Log.d("NavigationViewModel", "Force navigating to Home")
        _navigationEvent.value = NavigationEvent.NavigateToHome
        lastNavigationTime = System.currentTimeMillis()
        _isNavigating.value = true
    }
}
